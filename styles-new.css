/* Reset and base styles */
* {
	margin: 0;
	padding: 0;
	box-sizing: border-box;
}

body {
	font-family: "Inter", sans-serif;
	background: #0a0a0a;
	color: white;
	overflow-x: hidden;
	line-height: 1.6;
}

/* Container for 1440px design */
.container {
	max-width: 1440px;
	margin: 0 auto;
	padding: 0 80px;
}

/* Header Styles */
.header {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	z-index: 1000;
	background: rgba(10, 10, 10, 0.9);
	backdrop-filter: blur(10px);
	padding: 20px 0;
}

.nav-container {
	max-width: 1440px;
	margin: 0 auto;
	padding: 0 80px;
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.logo h2 {
	font-size: 24px;
	font-weight: 700;
	color: white;
}

.navigation {
	flex: 1;
	display: flex;
	justify-content: center;
}

.nav-menu {
	display: flex;
	list-style: none;
	gap: 40px;
}

.nav-menu a {
	color: white;
	text-decoration: none;
	font-weight: 500;
	font-size: 16px;
	transition: color 0.3s ease;
}

.nav-menu a:hover {
	color: #ff4444;
}

/* But<PERSON> Styles */
.btn {
	padding: 14px 28px;
	border: none;
	border-radius: 8px;
	font-weight: 600;
	font-size: 16px;
	cursor: pointer;
	transition: all 0.3s ease;
	text-decoration: none;
	display: inline-block;
	font-family: "Inter", sans-serif;
}

.btn-primary {
	background: linear-gradient(135deg, #ff4444 0%, #cc3333 100%);
	color: white;
}

.btn-primary:hover {
	background: linear-gradient(135deg, #ff5555 0%, #dd4444 100%);
	transform: translateY(-2px);
	box-shadow: 0 8px 25px rgba(255, 68, 68, 0.3);
}

.btn-secondary {
	background: transparent;
	color: white;
	border: 2px solid #ff4444;
}

.btn-secondary:hover {
	background: #ff4444;
	color: white;
}

/* Hero Section */
.hero {
	min-height: 100vh;
	background: linear-gradient(135deg, rgba(42, 42, 42, 0.7) 0%, rgba(26, 26, 26, 0.8) 50%, rgba(42, 42, 42, 0.7) 100%),
		url("assets/backgrounds/bg.png");
	background-size: cover;
	background-position: center;
	background-repeat: no-repeat;
	background-attachment: fixed;
	position: relative;
	display: flex;
	align-items: center;
	padding-top: 100px;
}

.hero-content {
	max-width: 1440px;
	margin: 0 auto;
	padding: 0 80px;
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: flex-start;
	min-height: 70vh;
	text-align: left;
}

.hero-text {
	max-width: 600px;
	z-index: 10;
}

.hero-title {
	font-size: 72px;
	font-weight: 800;
	line-height: 1.1;
	margin-bottom: 32px;
}

.highlight {
	background: linear-gradient(135deg, #ff4444 0%, #cc3333 100%);
	-webkit-background-clip: text;
	-webkit-text-fill-color: transparent;
	background-clip: text;
}

.hero-description {
	font-size: 20px;
	line-height: 1.6;
	color: #cccccc;
	margin-bottom: 48px;
	max-width: 500px;
}

.hero-buttons {
	display: flex;
	gap: 24px;
}

/* Stats Section */
.stats-section {
	background: #111111;
	padding: 80px 0;
}

.stats-grid {
	display: grid;
	grid-template-columns: repeat(3, 1fr);
	gap: 80px;
	text-align: center;
}

.stat-item {
	padding: 40px 20px;
}

.stat-number {
	font-size: 48px;
	font-weight: 800;
	color: #ff4444;
	margin-bottom: 16px;
}

.stat-label {
	font-size: 18px;
	color: #cccccc;
	font-weight: 500;
}

/* Section Styles */
.section-title {
	font-size: 56px;
	font-weight: 800;
	line-height: 1.1;
	margin-bottom: 32px;
	color: #1a1a1a;
}

.section-title.white {
	color: white;
}

.section-title.center {
	text-align: center;
}

.section-description {
	font-size: 18px;
	line-height: 1.6;
	color: #666666;
	margin-bottom: 40px;
	max-width: 500px;
}

.section-description.white {
	color: #cccccc;
}

/* Digitizing Section */
.digitizing-section {
	background: #f8f8f8;
	padding: 120px 0;
}

.digitizing-content {
	display: grid;
	grid-template-columns: 1fr 1fr;
	gap: 80px;
	align-items: center;
}

.digitizing-image img {
	width: 100%;
	height: 400px;
	object-fit: cover;
	border-radius: 12px;
}

/* Ecosystem Section */
.ecosystem-section {
	background: #1a1a1a;
	padding: 120px 0;
}

.ecosystem-content {
	text-align: center;
}

.ecosystem-grid {
	display: grid;
	grid-template-columns: repeat(2, 1fr);
	gap: 40px;
	margin-top: 80px;
}

.ecosystem-item {
	background: #2a2a2a;
	padding: 40px;
	border-radius: 12px;
	text-align: left;
}

.ecosystem-item h3 {
	font-size: 24px;
	font-weight: 700;
	color: #ff4444;
	margin-bottom: 16px;
}

.ecosystem-item p {
	font-size: 16px;
	color: #cccccc;
	line-height: 1.6;
}

/* Power Section */
.power-section {
	background: #f8f8f8;
	padding: 120px 0;
}

.power-content {
	display: grid;
	grid-template-columns: 1fr 1fr;
	gap: 80px;
	align-items: center;
}

.power-image img {
	width: 100%;
	height: 400px;
	object-fit: cover;
	border-radius: 12px;
}

/* Marketplace Section */
.marketplace-section {
	background: #f8f8f8;
	padding: 120px 0;
}

.marketplace-content {
	display: grid;
	grid-template-columns: 1fr 1fr;
	gap: 80px;
	align-items: center;
}

.marketplace-image img {
	width: 100%;
	height: 400px;
	object-fit: cover;
	border-radius: 12px;
}

/* Racing Section */
.racing-section {
	background: #1a1a1a;
	padding: 120px 0;
}

.racing-content {
	display: grid;
	grid-template-columns: 1fr 1fr;
	gap: 80px;
	align-items: center;
}

.racing-grid {
	display: grid;
	grid-template-columns: repeat(2, 1fr);
	gap: 20px;
}

.racing-grid img {
	width: 100%;
	height: 200px;
	object-fit: cover;
	border-radius: 8px;
}

/* Leaders Section */
.leaders-section {
	background: #1a1a1a;
	padding: 120px 0;
}

.leaders-grid {
	display: grid;
	grid-template-columns: repeat(3, 1fr);
	gap: 40px;
	margin-top: 80px;
}

.leader-card {
	background: #2a2a2a;
	padding: 30px;
	border-radius: 12px;
	text-align: center;
	transition: transform 0.3s ease;
}

.leader-card:hover {
	transform: translateY(-5px);
}

.leader-image {
	width: 80px;
	height: 80px;
	border-radius: 50%;
	object-fit: cover;
	margin: 0 auto 20px;
}

.leader-name {
	font-size: 20px;
	font-weight: 700;
	color: white;
	margin-bottom: 8px;
}

.leader-title {
	font-size: 16px;
	color: #ff4444;
	font-weight: 600;
	margin-bottom: 12px;
}

.leader-description {
	font-size: 14px;
	color: #cccccc;
	line-height: 1.5;
}

/* Footer */
.footer {
	background: #0a0a0a;
	padding: 80px 0 40px;
}

.footer-content {
	display: grid;
	grid-template-columns: 1fr 1fr;
	gap: 80px;
	margin-bottom: 60px;
}

.footer-title {
	font-size: 48px;
	font-weight: 800;
	line-height: 1.1;
	margin-bottom: 24px;
}

.footer-description {
	font-size: 18px;
	color: #cccccc;
	margin-bottom: 32px;
	max-width: 400px;
}

.footer-links {
	display: grid;
	grid-template-columns: repeat(3, 1fr);
	gap: 40px;
}

.footer-column h4 {
	font-size: 18px;
	font-weight: 700;
	color: white;
	margin-bottom: 20px;
}

.footer-column ul {
	list-style: none;
}

.footer-column ul li {
	margin-bottom: 12px;
}

.footer-column ul li a {
	color: #cccccc;
	text-decoration: none;
	font-size: 16px;
	transition: color 0.3s ease;
}

.footer-column ul li a:hover {
	color: #ff4444;
}

.footer-bottom {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding-top: 40px;
	border-top: 1px solid #333;
}

.footer-social {
	display: flex;
	gap: 24px;
}

.footer-social a {
	color: #cccccc;
	text-decoration: none;
	font-size: 16px;
	transition: color 0.3s ease;
}

.footer-social a:hover {
	color: #ff4444;
}

/* Responsive Design */
@media (max-width: 1200px) {
	.container {
		padding: 0 40px;
	}

	.nav-container {
		padding: 0 40px;
	}

	.hero-content {
		padding: 0 40px;
	}
}

@media (max-width: 768px) {
	.container {
		padding: 0 20px;
	}

	.nav-container {
		padding: 0 20px;
	}

	.hero-content {
		padding: 0 20px;
	}
}

@media (max-width: 768px) {
	.hero-title {
		font-size: 48px;
	}

	.section-title {
		font-size: 36px;
	}

	.stats-grid {
		grid-template-columns: 1fr;
		gap: 40px;
	}

	.digitizing-content,
	.power-content,
	.marketplace-content,
	.racing-content {
		grid-template-columns: 1fr;
		gap: 40px;
	}

	.leaders-grid {
		grid-template-columns: repeat(2, 1fr);
	}

	.footer-content {
		grid-template-columns: 1fr;
		gap: 40px;
	}

	.footer-links {
		grid-template-columns: repeat(2, 1fr);
	}
}

@media (max-width: 480px) {
	.hero-title {
		font-size: 36px;
	}

	.section-title {
		font-size: 28px;
	}

	.leaders-grid {
		grid-template-columns: 1fr;
	}

	.footer-links {
		grid-template-columns: 1fr;
	}

	.footer-bottom {
		flex-direction: column;
		gap: 20px;
	}
}
